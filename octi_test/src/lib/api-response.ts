import { NextResponse } from 'next/server'
import { z } from 'zod'

/**
 * API响应工具类
 * 统一处理API响应格式
 */

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
}

/**
 * 成功响应
 */
export function successResponse<T>(data: T, status: number = 200) {
  return NextResponse.json({
    success: true,
    data
  } as ApiResponse<T>, { status })
}

/**
 * 验证错误响应
 */
export function validationErrorResponse(error: z.ZodError) {
  return NextResponse.json({
    success: false,
    error: {
      code: 'VALIDATION_ERROR',
      message: '请求参数验证失败',
      details: error.errors
    }
  } as ApiResponse, { status: 400 })
}

/**
 * 服务器错误响应
 */
export function serverErrorResponse(message: string = '服务器内部错误') {
  return NextResponse.json({
    success: false,
    error: {
      code: 'INTERNAL_ERROR',
      message
    }
  } as ApiResponse, { status: 500 })
}

/**
 * 未找到错误响应
 */
export function notFoundResponse(message: string = '资源未找到') {
  return NextResponse.json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message
    }
  } as ApiResponse, { status: 404 })
}

/**
 * 未授权错误响应
 */
export function unauthorizedResponse(message: string = '未授权访问') {
  return NextResponse.json({
    success: false,
    error: {
      code: 'UNAUTHORIZED',
      message
    }
  } as ApiResponse, { status: 401 })
}