import { Logger } from '@/lib/logger'
import { z } from 'zod'

// 数据源Schema
const DataSourceSchema = z.object({
  sourceId: z.string(),
  content: z.string(),
  contentType: z.enum(['text', 'json', 'pdf', 'docx', 'url']),
  metadata: z.object({
    timestamp: z.string().optional(),
    reliability: z.number().min(0).max(1).optional(),
    source: z.string().optional()
  }).optional()
})

// 融合配置Schema
const FusionConfigSchema = z.object({
  strategy: z.enum(['weighted', 'priority', 'consensus']).default('weighted'),
  weights: z.record(z.number()).optional(),
  threshold: z.number().min(0).max(1).default(0.7),
  maxSources: z.number().positive().default(5)
})

export type DataSource = z.infer<typeof DataSourceSchema>
export type FusionConfig = z.infer<typeof FusionConfigSchema>

/**
 * 数据融合引擎 - 负责整合多源数据用于AI分析
 */
export class DataFusionEngine {
  private logger = new Logger('DataFusionEngine')

  /**
   * 融合多个数据源
   */
  async fuseData(
    sources: DataSource[], 
    config: FusionConfig = {}
  ): Promise<{
    fusedContent: string
    confidence: number
    sourcesUsed: string[]
    metadata: Record<string, any>
  }> {
    try {
      // 验证输入
      const validatedSources = sources.map(source => DataSourceSchema.parse(source))
      const validatedConfig = FusionConfigSchema.parse(config)

      this.logger.info(`开始数据融合，共 ${validatedSources.length} 个数据源`)

      // 预处理数据源
      const processedSources = await this.preprocessSources(validatedSources)
      
      // 根据策略融合数据
      const fusionResult = await this.applyFusionStrategy(
        processedSources, 
        validatedConfig
      )

      // 计算置信度
      const confidence = this.calculateConfidence(processedSources, fusionResult)

      this.logger.info('数据融合完成', {
        sourcesCount: validatedSources.length,
        confidence,
        strategy: validatedConfig.strategy
      })

      return {
        fusedContent: fusionResult.content,
        confidence,
        sourcesUsed: fusionResult.sourcesUsed,
        metadata: {
          strategy: validatedConfig.strategy,
          processedAt: new Date().toISOString(),
          originalSourcesCount: validatedSources.length,
          qualityScore: this.assessDataQuality(processedSources)
        }
      }

    } catch (error) {
      this.logger.error('数据融合失败', { error })
      throw new Error(`数据融合失败: ${error}`)
    }
  }

  /**
   * 预处理数据源
   */
  private async preprocessSources(sources: DataSource[]): Promise<Array<DataSource & {
    processedContent: string
    qualityScore: number
    relevanceScore: number
  }>> {
    const processed = []

    for (const source of sources) {
      try {
        let processedContent = source.content
        
        // 根据内容类型进行预处理
        switch (source.contentType) {
          case 'json':
            processedContent = this.processJsonContent(source.content)
            break
          case 'pdf':
          case 'docx':
            processedContent = await this.processDocumentContent(source.content)
            break
          case 'url':
            processedContent = await this.processUrlContent(source.content)
            break
          case 'text':
          default:
            processedContent = this.processTextContent(source.content)
            break
        }

        // 评估数据质量
        const qualityScore = this.assessContentQuality(processedContent)
        const relevanceScore = this.assessContentRelevance(processedContent)

        processed.push({
          ...source,
          processedContent,
          qualityScore,
          relevanceScore
        })

      } catch (error) {
        this.logger.warn(`数据源预处理失败: ${source.sourceId}`, { error })
        // 继续处理其他数据源
      }
    }

    return processed
  }

  /**
   * 应用融合策略
   */
  private async applyFusionStrategy(
    sources: Array<DataSource & { processedContent: string; qualityScore: number; relevanceScore: number }>,
    config: FusionConfig
  ): Promise<{ content: string; sourcesUsed: string[] }> {
    
    // 过滤低质量数据源
    const filteredSources = sources.filter(source => 
      source.qualityScore >= config.threshold
    ).slice(0, config.maxSources)

    if (filteredSources.length === 0) {
      throw new Error('没有符合质量要求的数据源')
    }

    switch (config.strategy) {
      case 'weighted':
        return this.weightedFusion(filteredSources, config.weights || {})
      
      case 'priority':
        return this.priorityFusion(filteredSources)
      
      case 'consensus':
        return this.consensusFusion(filteredSources)
      
      default:
        throw new Error(`不支持的融合策略: ${config.strategy}`)
    }
  }

  /**
   * 加权融合策略
   */
  private weightedFusion(
    sources: Array<DataSource & { processedContent: string; qualityScore: number }>,
    weights: Record<string, number>
  ): { content: string; sourcesUsed: string[] } {
    
    let fusedContent = '# 数据融合报告\n\n'
    const sourcesUsed: string[] = []

    // 按权重排序
    const sortedSources = sources.sort((a, b) => {
      const weightA = weights[a.sourceId] || a.qualityScore
      const weightB = weights[b.sourceId] || b.qualityScore
      return weightB - weightA
    })

    for (const source of sortedSources) {
      const weight = weights[source.sourceId] || source.qualityScore
      fusedContent += `## 数据源: ${source.sourceId} (权重: ${weight.toFixed(2)})\n\n`
      fusedContent += `${source.processedContent}\n\n`
      sourcesUsed.push(source.sourceId)
    }

    return { content: fusedContent, sourcesUsed }
  }

  /**
   * 优先级融合策略
   */
  private priorityFusion(
    sources: Array<DataSource & { processedContent: string; qualityScore: number; relevanceScore: number }>
  ): { content: string; sourcesUsed: string[] } {
    
    // 按综合评分排序
    const sortedSources = sources.sort((a, b) => {
      const scoreA = (a.qualityScore + a.relevanceScore) / 2
      const scoreB = (b.qualityScore + b.relevanceScore) / 2
      return scoreB - scoreA
    })

    let fusedContent = '# 优先级数据融合报告\n\n'
    const sourcesUsed: string[] = []

    for (const source of sortedSources) {
      const score = (source.qualityScore + source.relevanceScore) / 2
      fusedContent += `## 数据源: ${source.sourceId} (评分: ${score.toFixed(2)})\n\n`
      fusedContent += `${source.processedContent}\n\n`
      sourcesUsed.push(source.sourceId)
    }

    return { content: fusedContent, sourcesUsed }
  }

  /**
   * 共识融合策略
   */
  private consensusFusion(
    sources: Array<DataSource & { processedContent: string }>
  ): { content: string; sourcesUsed: string[] } {
    
    // 简化实现：提取共同关键词和主题
    const allContent = sources.map(s => s.processedContent).join(' ')
    const keywords = this.extractKeywords(allContent)
    const commonThemes = this.identifyCommonThemes(sources.map(s => s.processedContent))

    let fusedContent = '# 共识数据融合报告\n\n'
    fusedContent += `## 关键词汇\n${keywords.join(', ')}\n\n`
    fusedContent += `## 共同主题\n${commonThemes.join('\n')}\n\n`
    fusedContent += `## 详细内容\n\n`

    const sourcesUsed: string[] = []
    for (const source of sources) {
      fusedContent += `### ${source.sourceId}\n${source.processedContent}\n\n`
      sourcesUsed.push(source.sourceId)
    }

    return { content: fusedContent, sourcesUsed }
  }

  /**
   * 处理不同类型的内容
   */
  private processJsonContent(content: string): string {
    try {
      const parsed = JSON.parse(content)
      return JSON.stringify(parsed, null, 2)
    } catch {
      return content
    }
  }

  private processTextContent(content: string): string {
    // 清理和格式化文本
    return content
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n/g, '\n\n')
      .trim()
  }

  private async processDocumentContent(content: string): Promise<string> {
    // 这里应该实现文档解析逻辑
    // 暂时返回原内容
    return content
  }

  private async processUrlContent(content: string): Promise<string> {
    // 这里应该实现URL内容抓取逻辑
    // 暂时返回原内容
    return content
  }

  /**
   * 评估内容质量
   */
  private assessContentQuality(content: string): number {
    let score = 0.5 // 基础分

    // 长度评分
    if (content.length > 100) score += 0.1
    if (content.length > 500) score += 0.1
    if (content.length > 1000) score += 0.1

    // 结构评分
    if (content.includes('\n')) score += 0.05
    if (/[.!?]/.test(content)) score += 0.05

    // 内容丰富度评分
    const uniqueWords = new Set(content.toLowerCase().split(/\s+/)).size
    if (uniqueWords > 50) score += 0.1
    if (uniqueWords > 100) score += 0.1

    return Math.min(score, 1.0)
  }

  /**
   * 评估内容相关性
   */
  private assessContentRelevance(content: string): number {
    // OCTI相关关键词
    const octiKeywords = [
      '组织', '团队', '领导', '管理', '文化', '能力', '评估',
      '结构化', '灵活', '直觉', '思考', '愿景', '行动', '深思'
    ]

    const contentLower = content.toLowerCase()
    const matchedKeywords = octiKeywords.filter(keyword => 
      contentLower.includes(keyword)
    )

    return Math.min(matchedKeywords.length / octiKeywords.length * 2, 1.0)
  }

  /**
   * 计算融合置信度
   */
  private calculateConfidence(
    sources: Array<{ qualityScore: number; relevanceScore: number }>,
    result: { sourcesUsed: string[] }
  ): number {
    if (sources.length === 0) return 0

    const usedSources = sources.slice(0, result.sourcesUsed.length)
    const avgQuality = usedSources.reduce((sum, s) => sum + s.qualityScore, 0) / usedSources.length
    const avgRelevance = usedSources.reduce((sum, s) => sum + s.relevanceScore, 0) / usedSources.length
    
    // 综合质量、相关性和数据源数量
    const sourceCountFactor = Math.min(usedSources.length / 3, 1.0)
    
    return (avgQuality * 0.4 + avgRelevance * 0.4 + sourceCountFactor * 0.2)
  }

  /**
   * 评估整体数据质量
   */
  private assessDataQuality(sources: Array<{ qualityScore: number }>): number {
    if (sources.length === 0) return 0
    return sources.reduce((sum, s) => sum + s.qualityScore, 0) / sources.length
  }

  /**
   * 提取关键词
   */
  private extractKeywords(content: string): string[] {
    const words = content.toLowerCase()
      .replace(/[^\w\s\u4e00-\u9fff]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2)

    const wordCount = new Map<string, number>()
    for (const word of words) {
      wordCount.set(word, (wordCount.get(word) || 0) + 1)
    }

    return Array.from(wordCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([word]) => word)
  }

  /**
   * 识别共同主题
   */
  private identifyCommonThemes(contents: string[]): string[] {
    // 简化实现：查找在多个内容中都出现的句子模式
    const themes: string[] = []
    
    // 这里应该实现更复杂的主题识别算法
    // 暂时返回基础主题
    themes.push('组织能力评估相关内容')
    
    return themes
  }
}