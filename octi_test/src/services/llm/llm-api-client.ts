import { Logger } from '@/lib/logger'
import { z } from 'zod'

// LLM请求配置Schema
const LLMRequestSchema = z.object({
  model: z.string(),
  messages: z.array(z.object({
    role: z.enum(['system', 'user', 'assistant']),
    content: z.string()
  })),
  temperature: z.number().min(0).max(2).optional(),
  maxTokens: z.number().positive().optional(),
  stream: z.boolean().optional()
})

// LLM响应Schema
const LLMResponseSchema = z.object({
  id: z.string(),
  choices: z.array(z.object({
    message: z.object({
      role: z.string(),
      content: z.string()
    }),
    finishReason: z.string().optional()
  })),
  usage: z.object({
    promptTokens: z.number(),
    completionTokens: z.number(),
    totalTokens: z.number()
  }).optional()
})

export type LLMRequest = z.infer<typeof LLMRequestSchema>
export type LLMResponse = z.infer<typeof LLMResponseSchema>

/**
 * LLM API客户端 - 统一的大语言模型调用接口
 */
export class LLMApiClient {
  private logger = new Logger('LLMApiClient')
  private readonly MINIMAX_BASE_URL = 'https://api.minimax.chat/v1'
  private readonly DEEPSEEK_BASE_URL = 'https://api.deepseek.com/v1'
  private readonly DEFAULT_TIMEOUT = 30000
  private readonly MAX_RETRIES = 3

  /**
   * 调用MiniMax API
   */
  async callMiniMax(request: LLMRequest): Promise<LLMResponse> {
    const apiKey = process.env.MINIMAX_API_KEY
    if (!apiKey) {
      throw new Error('MiniMax API密钥未配置')
    }

    return this.makeRequest(
      `${this.MINIMAX_BASE_URL}/text/chatcompletion`,
      {
        ...request,
        model: request.model || 'abab6.5-chat'
      },
      {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    )
  }

  /**
   * 调用DeepSeek API
   */
  async callDeepSeek(request: LLMRequest): Promise<LLMResponse> {
    const apiKey = process.env.DEEPSEEK_API_KEY
    if (!apiKey) {
      throw new Error('DeepSeek API密钥未配置')
    }

    return this.makeRequest(
      `${this.DEEPSEEK_BASE_URL}/chat/completions`,
      {
        ...request,
        model: request.model || 'deepseek-chat'
      },
      {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    )
  }

  /**
   * 统一的HTTP请求方法
   */
  private async makeRequest(
    url: string, 
    data: any, 
    headers: Record<string, string>
  ): Promise<LLMResponse> {
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
      try {
        this.logger.debug(`LLM API请求 (尝试 ${attempt}/${this.MAX_RETRIES})`, { 
          url: url.replace(/\/v1.*/, '/v1/***'),
          model: data.model 
        })

        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), this.DEFAULT_TIMEOUT)

        const response = await fetch(url, {
          method: 'POST',
          headers,
          body: JSON.stringify(data),
          signal: controller.signal
        })

        clearTimeout(timeoutId)

        if (!response.ok) {
          const errorText = await response.text()
          throw new Error(`HTTP ${response.status}: ${errorText}`)
        }

        const responseData = await response.json()
        const validatedResponse = LLMResponseSchema.parse(responseData)

        this.logger.info('LLM API调用成功', {
          model: data.model,
          tokensUsed: validatedResponse.usage?.totalTokens || 0,
          attempt
        })

        return validatedResponse

      } catch (error) {
        lastError = error as Error
        this.logger.warn(`LLM API调用失败 (尝试 ${attempt}/${this.MAX_RETRIES})`, { 
          error: lastError.message,
          model: data.model 
        })

        // 如果不是最后一次尝试，等待后重试
        if (attempt < this.MAX_RETRIES) {
          const delay = Math.pow(2, attempt) * 1000 // 指数退避
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }

    this.logger.error('LLM API调用最终失败', { 
      error: lastError?.message,
      attempts: this.MAX_RETRIES 
    })
    throw lastError || new Error('LLM API调用失败')
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{ minimax: boolean; deepseek: boolean }> {
    const results = {
      minimax: false,
      deepseek: false
    }

    // 检查MiniMax
    try {
      await this.callMiniMax({
        model: 'abab6.5-chat',
        messages: [{ role: 'user', content: 'ping' }],
        maxTokens: 10
      })
      results.minimax = true
    } catch (error) {
      this.logger.warn('MiniMax健康检查失败', { error })
    }

    // 检查DeepSeek
    try {
      await this.callDeepSeek({
        model: 'deepseek-chat',
        messages: [{ role: 'user', content: 'ping' }],
        maxTokens: 10
      })
      results.deepseek = true
    } catch (error) {
      this.logger.warn('DeepSeek健康检查失败', { error })
    }

    return results
  }
}