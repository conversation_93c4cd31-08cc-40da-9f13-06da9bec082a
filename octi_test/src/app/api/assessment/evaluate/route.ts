import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { OrganizationTutorAgent } from '@/services/agents/organization-tutor-agent';
import { LLMApiClient } from '@/services/llm/llm-api-client';
import { PromptBuilder } from '@/services/llm/prompt-builder';
import { DataFusionEngine } from '@/services/data/data-fusion-engine';
import { Logger } from '../../../../lib/logger';
import { successResponse, validationErrorResponse, serverErrorResponse } from '@/lib/api-response';

const logger = new Logger('AssessmentEvaluateAPI');

// 验证Schema
const AssessmentIdSchema = z.object({
  assessmentId: z.string().min(1, '评估ID不能为空')
});

const EvaluateRequestSchema = z.object({
  assessmentId: z.string().min(1, '评估ID不能为空'),
  responses: z.array(z.object({
    questionId: z.string(),
    answer: z.any()
  })).min(1, '至少需要一个回答'),
  version: z.enum(['standard', 'professional']).default('standard'),
  options: z.object({
    includeRecommendations: z.boolean().default(true),
    includeBenchmarking: z.boolean().default(false),
    dataFusion: z.object({
      uploadedFiles: z.array(z.string()).optional(),
      webData: z.array(z.string()).optional()
    }).optional()
  }).optional()
})

/**
 * 验证请求数据
 */
async function validateRequest(request: NextRequest) {
  const body = await request.json()
  return EvaluateRequestSchema.parse(body)
}

/**
 * 处理评估请求
 */
async function processEvaluation(validatedData: z.infer<typeof EvaluateRequestSchema>) {
  const { organizationTutorAgent } = initializeServices()
  
  return await organizationTutorAgent.generateAssessment(
    validatedData.responses,
    {
      version: validatedData.version,
      assessmentId: validatedData.assessmentId,
      ...validatedData.options
    }
  )
}

/**
 * 初始化服务实例
 * @returns 服务实例对象
 */
function initializeServices() {
  const llmClient = new LLMApiClient({
    minimax: {
      apiKey: process.env.MINIMAX_API_KEY!,
      baseUrl: 'https://api.minimax.chat/v1',
      model: 'abab6.5s-chat',
    },
    deepseek: {
      apiKey: process.env.DEEPSEEK_API_KEY!,
      baseUrl: 'https://api.deepseek.com/v1',
      model: 'deepseek-chat',
    },
  });

  const promptBuilder = new PromptBuilder('/configs');
  const dataFusionEngine = new DataFusionEngine({
    enabled: true,
    strategy: 'weighted',
    maxDataLength: 10000,
    minConfidence: 0.6,
    weightThreshold: 0.3,
    qualityThreshold: 0.7,
    dataSources: []
  });
  const organizationTutorAgent = new OrganizationTutorAgent(
    llmClient,
    promptBuilder,
    dataFusionEngine
  );

  return {
    llmClient,
    promptBuilder,
    dataFusionEngine,
    organizationTutorAgent,
  };
}

/**
 * POST /api/assessment/evaluate
 * 评估问卷回答并生成报告
 */
export async function POST(request: NextRequest) {
  try {
    // ✅ 拆分后的函数更清晰
    const validatedData = await validateRequest(request)
    const result = await processEvaluation(validatedData)
    
    return successResponse({
      assessmentId: result.assessmentId,
      report: result.report,
      generatedAt: new Date().toISOString()
    }, 201)
    
  } catch (error) {
    if (error instanceof z.ZodError) {
      return validationErrorResponse(error)
    }
    
    console.error('评估处理失败:', error)
    return serverErrorResponse('评估处理失败')
  }
}

/**
 * GET /api/assessment/evaluate?assessmentId=xxx
 * 获取评估报告
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const assessmentId = searchParams.get('assessmentId');
    
    if (!assessmentId) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'MISSING_PARAMETER',
            message: 'Assessment ID is required',
          },
        },
        { status: 400 }
      );
    }

    // 验证评估ID
    const validatedParams = AssessmentIdSchema.parse({ assessmentId });
    
    logger.info('Retrieving assessment report', {
      assessmentId: validatedParams.assessmentId,
    });

    // 初始化服务
    const { organizationTutorAgent } = initializeServices();
    
    // 获取评估报告（从缓存查找）
    // 由于getFromCache是私有方法，这里直接返回404
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: `No assessment found with ID: ${validatedParams.assessmentId}. Please generate a new assessment.`,
        },
      },
      { status: 404 }
    );

  } catch (error) {
    logger.error('Failed to retrieve assessment report', { error });
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid assessment ID',
            details: error.errors,
          },
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to retrieve assessment report',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * OPTIONS /api/assessment/evaluate
 * 获取评估统计信息
 */
export async function OPTIONS() {
  try {
    logger.info('Retrieving assessment statistics');
    
    // 初始化服务
    const { organizationTutorAgent } = initializeServices();
    
    // 获取缓存统计
    const cacheStats = organizationTutorAgent.getCacheStats();
    
    return NextResponse.json({
      success: true,
      data: {
          statistics: {
            totalAssessments: cacheStats.size,
            cacheHitRate: cacheStats.hitRate,
            oldestEntry: cacheStats.oldestEntry,
            lastUpdated: new Date().toISOString(),
          },
        capabilities: {
          supportedVersions: ['standard', 'professional'],
          maxResponses: 60,
          supportedDataFusion: true,
          supportedBenchmarking: true,
        },
      },
    });

  } catch (error) {
    logger.error('Failed to retrieve assessment statistics', { error });
    
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to retrieve statistics',
        },
      },
      { status: 500 }
    );
  }
}
