import { NextRequest, NextResponse } from 'next/server'
import { ApiService } from '@/services/api/ApiService'
import { AssessmentApiRequest } from '@/services/api/ApiService'
import { z } from 'zod'
import { 
  successResponse, 
  errorResponse, 
  validationErrorResponse, 
  serverErrorResponse 
} from '@/lib/api-response'

let apiService: ApiService | null = null

/**
 * 获取API服务实例
 */
async function getApiService(): Promise<ApiService> {
  if (!apiService) {
    apiService = ApiService.getInstance()
    await apiService.initialize()
  }
  return apiService
}

/**
 * GET /api/assessments - 获取评估列表或单个评估
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const assessmentId = searchParams.get('id')

    const service = await getApiService()
    
    if (assessmentId) {
      // 获取单个评估
      const apiRequest: AssessmentApiRequest = {
        action: 'get',
        assessmentId
      }

      const result = await service.handleAssessmentRequest(apiRequest)
      
      if (result.success) {
        return NextResponse.json(result, { status: 200 })
      } else {
        return NextResponse.json(result, { status: 404 })
      }
    } else {
      // 获取评估列表（模拟实现）
      const mockAssessments = {
        success: true,
        data: [
          {
            id: 'assessment_1',
            title: 'OCTI智能评估 - 团队协作',
            status: 'active',
            createdAt: new Date().toISOString()
          },
          {
            id: 'assessment_2',
            title: 'OCTI智能评估 - 领导力',
            status: 'completed',
            createdAt: new Date().toISOString()
          }
        ]
      }
      
      return NextResponse.json(mockAssessments, { status: 200 })
    }
  } catch (error) {
    console.error('评估API GET请求失败:', error)
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    )
  }
}

// 定义验证Schema
const CreateAssessmentSchema = z.object({
  title: z.string().min(1, '标题不能为空').max(100, '标题不能超过100字符'),
  description: z.string().optional(),
  type: z.enum(['team_collaboration', 'leadership', 'innovation'], {
    errorMap: () => ({ message: '无效的评估类型' })
  }),
  dimensions: z.array(z.string()).min(1, '至少需要一个维度').max(10, '维度不能超过10个'),
  requirements: z.object({
    minQuestions: z.number().min(5).max(100).optional(),
    maxDuration: z.number().min(10).max(120).optional(),
    version: z.enum(['standard', 'professional']).default('standard')
  }).optional()
})

/**
 * POST /api/assessments - 创建新评估
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = CreateAssessmentSchema.parse(body)
    
    const service = await getApiService()
    const result = await service.handleAssessmentRequest({
      action: 'create',
      data: validatedData
    })
    
    if (result.success) {
      return successResponse(result.data, 201)
    } else {
      return errorResponse('CREATE_FAILED', result.error || '创建评估失败')
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return validationErrorResponse(error)
    }
    
    console.error('创建评估失败:', error)
    return serverErrorResponse()
  }
}

/**
 * PUT /api/assessments - 提交评估答案
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { assessmentId, answers } = body

    if (!assessmentId || !answers) {
      return NextResponse.json(
        { success: false, error: '缺少必要参数: assessmentId 或 answers' },
        { status: 400 }
      )
    }

    const service = await getApiService()
    
    const apiRequest: AssessmentApiRequest = {
      action: 'submit',
      assessmentId,
      data: { answers }
    }

    const result = await service.handleAssessmentRequest(apiRequest)
    
    if (result.success) {
      return NextResponse.json(result, { status: 200 })
    } else {
      return NextResponse.json(result, { status: 400 })
    }
  } catch (error) {
    console.error('提交评估失败:', error)
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    )
  }
}
