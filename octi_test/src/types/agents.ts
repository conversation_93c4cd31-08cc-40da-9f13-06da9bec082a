/**
 * 统一的智能体类型定义
 */
export interface AgentInput {
  [key: string]: any;
}

export interface AgentOutput {
  success: boolean;
  data?: any;
  error?: string;
  metadata?: {
    model?: string;
    temperature?: number;
    tokensUsed?: number;
    processingTime?: number;
    [key: string]: any;
  };
}

export interface AgentStatus {
  name: string;
  initialized: boolean;
  lastActivity: Date;
  config: Record<string, any>;
}

// 问卷设计师专用接口
export interface QuestionnaireDesignInput extends AgentInput {
  assessmentType: string;
  dimensions: string[];
  requirements?: string;
  version: 'standard' | 'professional';
  organizationType?: string;
  industryContext?: string;
}

// 组织评估导师专用接口
export interface AssessmentAnalysisInput extends AgentInput {
  assessmentData: AssessmentData;
  options: TutorOptions;
}