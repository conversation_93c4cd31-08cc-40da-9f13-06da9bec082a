"use strict";exports.id=447,exports.ids=[447],exports.modules={3966:(e,t,s)=>{s.d(t,{Yd:()=>i});class i{constructor(e="App"){this.context=e}formatMessage(e,t,s){let i=new Date().toISOString(),n=s?` ${JSON.stringify(s)}`:"";return`[${i}] [${e.toUpperCase()}] [${this.context}] ${t}${n}`}debug(e,t){"debug"===process.env.LOG_LEVEL&&console.debug(this.formatMessage("debug",e,t))}info(e,t){console.info(this.formatMessage("info",e,t))}warn(e,t){console.warn(this.formatMessage("warn",e,t))}error(e,t){console.error(this.formatMessage("error",e,t))}setContext(e){this.context=e}}new i("OCTI")},3686:(e,t,s)=>{s.d(t,{o:()=>n});let i=new(s(3966)).Yd("QuestionDesignerAgent");class n{constructor(e,t,s){this.name="question_designer",this.questionCache=new Map,this.isInitialized=!1,this.llmClient=e,this.promptBuilder=t,this.dataFusionEngine=s}async initialize(){if(this.isInitialized){i.warn("QuestionDesignerAgent 已经初始化");return}try{if(!this.llmClient)throw Error("LLMApiClient 未正确初始化");if(!this.promptBuilder)throw Error("PromptBuilder 未正确初始化");this.questionCache.clear(),this.isInitialized=!0,i.info("QuestionDesignerAgent 初始化完成")}catch(e){throw i.error("QuestionDesignerAgent 初始化失败",{error:e}),e}}isInit(){return this.isInitialized}getStatus(){return{name:this.name,initialized:this.isInitialized,lastActivity:new Date,config:{}}}async designQuestionnaire(e){if(!this.isInitialized)throw Error("智能体未初始化，请先调用 initialize()");let t=Date.now();try{let s;let n=this.generateCacheKey(e);if(this.questionCache.has(n))return i.debug("Using cached questionnaire"),this.questionCache.get(n);i.info("Starting questionnaire design",{version:e.version,organizationType:e.organizationType,hasExternalData:!!e.externalData?.length});let r=[];if("professional"===e.version&&e.externalData&&this.dataFusionEngine){let t=await this.dataFusionEngine.processRawData(e.externalData);r=(await this.dataFusionEngine.fuseData(t)).metadata.dataSourcesUsed}let o={version:e.version,agentType:"question_designer",context:{organizationType:e.organizationType,industryContext:e.industryContext,targetAudience:e.targetAudience,customRequirements:e.customRequirements},externalData:e.externalData?.map(e=>({source:e.sourceId,content:e.content,type:e.contentType})),dataFusion:e.dataFusion},a=await this.promptBuilder.buildPrompt(o),l={messages:[{role:"system",content:a.systemPrompt},{role:"user",content:a.userPrompt}],temperature:.7,maxTokens:"professional"===e.version?6e3:4e3};s="professional"===e.version?(await this.llmClient.dualModelChat(l,"sequential")).secondary:await this.llmClient.chat("deepseek",l);let u=await this.parseLLMResponse(s.content,e,r),c=await this.validateQuestionnaire(u);if(!c.isValid&&(i.warn("Generated questionnaire failed validation",{errors:c.errors,qualityScore:c.qualityScore}),c.qualityScore<.6))return this.regenerateQuestionnaire(e,c.suggestions);this.questionCache.set(n,u);let d=Date.now()-t;return i.info("Questionnaire design completed",{questionnaireId:u.id,totalQuestions:u.questions.length,version:u.version,qualityScore:c.qualityScore,processingTime:d}),u}catch(t){throw i.error("Questionnaire design failed",{error:t,options:e}),Error(`Failed to design questionnaire: ${t instanceof Error?t.message:"Unknown error"}`)}}async parseLLMResponse(e,t,s){try{let n;let r=e.match(/```json\s*([\s\S]*?)\s*```/)||e.match(/\{[\s\S]*\}/);if(r){let t=r[1]||r[0];try{n=JSON.parse(t)}catch(s){i.warn("JSON解析失败，尝试结构化解析",{content:t.substring(0,200),error:s}),n=this.parseStructuredResponse(e)}}else n=this.parseStructuredResponse(e);if(!n||"object"!=typeof n)throw Error("LLM响应解析结果无效");let o={id:this.generateQuestionnaireId(),version:t.version,title:n.title||`OCTI组织文化评估问卷（${"professional"===t.version?"专业版":"标准版"}）`,description:n.description||"OCTI四维八极框架组织文化评估",instructions:n.instructions||"请根据您的实际情况如实回答以下问题",questions:this.parseQuestions(n.questions||[]),metadata:{totalQuestions:(n.questions||[]).length,estimatedTime:this.calculateEstimatedTime(n.questions||[]),difficulty:this.calculateDifficulty(n.questions||[]),createdAt:new Date,framework:"OCTI四维八极",dataSourcesUsed:s.length>0?s:void 0}};if(0===o.questions.length)throw Error("生成的问卷没有包含任何问题");return o}catch(t){throw i.error("LLM响应解析失败",{error:t instanceof Error?t.message:String(t),contentPreview:e.substring(0,500)}),Error(`问卷解析失败: ${t instanceof Error?t.message:"未知错误"}`)}}parseStructuredResponse(e){let t=e.split("\n").filter(e=>e.trim()),s={title:"",description:"",instructions:"",questions:[]},i=null;for(let e of t){let t=e.trim();t.includes("标题")||t.includes("title")?s.title=this.extractValue(t):t.includes("描述")||t.includes("description")?s.description=this.extractValue(t):t.includes("问题")&&t.includes(":")&&(i&&s.questions.push(i),i={id:`q_${s.questions.length+1}`,text:this.extractValue(t),type:"likert_scale",options:[]})}return i&&s.questions.push(i),s}extractValue(e){let t=e.indexOf(":");return t>-1?e.substring(t+1).trim():e}parseQuestions(e){return e.map((e,t)=>{let s={id:e.id||`q_${t+1}`,dimension:e.dimension||this.inferDimension(e.text),subdimension:e.subdimension||"",type:e.type||this.inferQuestionType(e.text),depth:e.depth||"intermediate",text:e.text||e.question||"",options:e.options,scale:e.scale,reversed:e.reversed||!1,weight:e.weight||1,metadata:{difficulty:e.difficulty||.5,discriminationIndex:e.discriminationIndex||.5,expectedResponseTime:e.expectedResponseTime||30}};return"single_choice"!==s.type&&"multiple_choice"!==s.type||s.options||(s.options=this.generateDefaultOptions(s.type)),"likert_scale"!==s.type||s.scale||(s.scale={min:1,max:5,labels:["完全不同意","不同意","中立","同意","完全同意"]}),s})}inferQuestionType(e){return e.includes("排序")||e.includes("排列")?"ranking":e.includes("多选")||e.includes("可以选择多个")?"multiple_choice":e.includes("同意")||e.includes("程度")?"likert_scale":e.includes("描述")||e.includes("说明")||e.includes("举例")?"open_ended":"single_choice"}inferDimension(e){return e.includes("权力")||e.includes("等级")||e.includes("层级")?"权力距离":e.includes("个人")||e.includes("集体")||e.includes("团队")?"个人主义vs集体主义":e.includes("竞争")||e.includes("合作")||e.includes("成就")?"男性化vs女性化":e.includes("不确定")||e.includes("风险")||e.includes("变化")?"不确定性规避":"综合"}generateDefaultOptions(e){return"single_choice"===e||"multiple_choice"===e?["完全不符合","基本不符合","部分符合","基本符合","完全符合"]:[]}async validateQuestionnaire(e){let t;let s=[],i=[],n=[];e.title||s.push("问卷标题不能为空"),0===e.questions.length&&s.push("问卷必须包含至少一个问题");let r="professional"===e.version?40:20;for(let t of(e.questions.length<.8*r&&i.push(`问题数量偏少，建议至少${r}个问题`),new Set(e.questions.map(e=>e.dimension)).size<4&&s.push("问卷应覆盖OCTI四个维度"),this.analyzeQuestionTypes(e.questions).likert_scale<.5&&n.push("建议增加更多量表题以提高测量精度"),e.questions))(!t.text||t.text.length<10)&&s.push(`问题${t.id}内容过短`),"single_choice"===t.type&&(!t.options||t.options.length<3)&&s.push(`问题${t.id}选项不足`);return t=Math.max(0,t=1-.2*s.length-.1*i.length),{isValid:0===s.length,errors:s,warnings:i,suggestions:n,qualityScore:t}}async regenerateQuestionnaire(e,t){i.info("Regenerating questionnaire with improvements",{suggestions:t});let s={...e,customRequirements:[e.customRequirements||"","请特别注意以下改进建议：",...t].filter(Boolean).join("\n")};return this.designQuestionnaire(s)}analyzeQuestionTypes(e){let t={single_choice:0,multiple_choice:0,likert_scale:0,open_ended:0,ranking:0};e.forEach(e=>{t[e.type]=(t[e.type]||0)+1});let s=e.length;return Object.keys(t).forEach(e=>{t[e]=t[e]/s}),t}calculateEstimatedTime(e){return e.reduce((e,t)=>{let s="open_ended"===t.type?60:30;return e+(t.expectedResponseTime||s)},0)}calculateDifficulty(e){return 0===e.length?.5:e.reduce((e,t)=>e+(t.difficulty||.5),0)/e.length}generateQuestionnaireId(){return`questionnaire_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}generateCacheKey(e){return[e.version,e.organizationType||"default",e.industryContext||"default",e.targetAudience||"default",e.customRequirements||"default"].join("_").replace(/[^a-zA-Z0-9_]/g,"_")}async getQuestionnairePreview(e){for(let t of Array.from(this.questionCache.values()))if(t.id===e)return{title:t.title,description:t.description,questionCount:t.questions.length,estimatedTime:t.metadata.estimatedTime,dimensions:Array.from(new Set(t.questions.map(e=>e.dimension)))};return null}clearCache(){this.questionCache.clear(),i.info("QuestionDesignerAgent cache cleared")}getStats(){let e=Array.from(this.questionCache.values()),t={};return e.forEach(e=>{t[e.version]=(t[e.version]||0)+1}),{cacheSize:this.questionCache.size,totalQuestionnaires:e.length,versionDistribution:t}}async generateQuestionnaireBatched(e){let t=["SF","IT","MV","AD"],s={version:e.version,total_questions:60,dimensions:{SF:{questions:[]},IT:{questions:[]},MV:{questions:[]},AD:{questions:[]}}},i=t.map(t=>this.generateDimensionBatch(t,0,5,e));return(await Promise.all(i)).forEach((e,i)=>{let n=t[i];s.dimensions[n].questions.push(...e)}),this.generateRemainingQuestions(s,e),s}async generateRemainingQuestions(e,t){}}},3320:(e,t,s)=>{s.d(t,{e:()=>a});var i=s(3966),n=s(5252);let r=n.Ry({sourceId:n.Z_(),content:n.Z_(),contentType:n.Km(["text","json","pdf","docx","url"]),metadata:n.Ry({timestamp:n.Z_().optional(),reliability:n.Rx().min(0).max(1).optional(),source:n.Z_().optional()}).optional()}),o=n.Ry({strategy:n.Km(["weighted","priority","consensus"]).default("weighted"),weights:n.IM(n.Rx()).optional(),threshold:n.Rx().min(0).max(1).default(.7),maxSources:n.Rx().positive().default(5)});class a{async fuseData(e,t={}){try{let s=e.map(e=>r.parse(e)),i=o.parse(t);this.logger.info(`开始数据融合，共 ${s.length} 个数据源`);let n=await this.preprocessSources(s),a=await this.applyFusionStrategy(n,i),l=this.calculateConfidence(n,a);return this.logger.info("数据融合完成",{sourcesCount:s.length,confidence:l,strategy:i.strategy}),{fusedContent:a.content,confidence:l,sourcesUsed:a.sourcesUsed,metadata:{strategy:i.strategy,processedAt:new Date().toISOString(),originalSourcesCount:s.length,qualityScore:this.assessDataQuality(n)}}}catch(e){throw this.logger.error("数据融合失败",{error:e}),Error(`数据融合失败: ${e}`)}}async preprocessSources(e){let t=[];for(let s of e)try{let e=s.content;switch(s.contentType){case"json":e=this.processJsonContent(s.content);break;case"pdf":case"docx":e=await this.processDocumentContent(s.content);break;case"url":e=await this.processUrlContent(s.content);break;default:e=this.processTextContent(s.content)}let i=this.assessContentQuality(e),n=this.assessContentRelevance(e);t.push({...s,processedContent:e,qualityScore:i,relevanceScore:n})}catch(e){this.logger.warn(`数据源预处理失败: ${s.sourceId}`,{error:e})}return t}async applyFusionStrategy(e,t){let s=e.filter(e=>e.qualityScore>=t.threshold).slice(0,t.maxSources);if(0===s.length)throw Error("没有符合质量要求的数据源");switch(t.strategy){case"weighted":return this.weightedFusion(s,t.weights||{});case"priority":return this.priorityFusion(s);case"consensus":return this.consensusFusion(s);default:throw Error(`不支持的融合策略: ${t.strategy}`)}}weightedFusion(e,t){let s="# 数据融合报告\n\n",i=[];for(let n of e.sort((e,s)=>{let i=t[e.sourceId]||e.qualityScore;return(t[s.sourceId]||s.qualityScore)-i})){let e=t[n.sourceId]||n.qualityScore;s+=`## 数据源: ${n.sourceId} (权重: ${e.toFixed(2)})

${n.processedContent}

`,i.push(n.sourceId)}return{content:s,sourcesUsed:i}}priorityFusion(e){let t=e.sort((e,t)=>{let s=(e.qualityScore+e.relevanceScore)/2;return(t.qualityScore+t.relevanceScore)/2-s}),s="# 优先级数据融合报告\n\n",i=[];for(let e of t){let t=(e.qualityScore+e.relevanceScore)/2;s+=`## 数据源: ${e.sourceId} (评分: ${t.toFixed(2)})

${e.processedContent}

`,i.push(e.sourceId)}return{content:s,sourcesUsed:i}}consensusFusion(e){let t=e.map(e=>e.processedContent).join(" "),s=this.extractKeywords(t),i=this.identifyCommonThemes(e.map(e=>e.processedContent)),n="# 共识数据融合报告\n\n";n+=`## 关键词汇
${s.join(", ")}

## 共同主题
${i.join("\n")}

## 详细内容

`;let r=[];for(let t of e)n+=`### ${t.sourceId}
${t.processedContent}

`,r.push(t.sourceId);return{content:n,sourcesUsed:r}}processJsonContent(e){try{let t=JSON.parse(e);return JSON.stringify(t,null,2)}catch{return e}}processTextContent(e){return e.replace(/\s+/g," ").replace(/\n\s*\n/g,"\n\n").trim()}async processDocumentContent(e){return e}async processUrlContent(e){return e}assessContentQuality(e){let t=.5;e.length>100&&(t+=.1),e.length>500&&(t+=.1),e.length>1e3&&(t+=.1),e.includes("\n")&&(t+=.05),/[.!?]/.test(e)&&(t+=.05);let s=new Set(e.toLowerCase().split(/\s+/)).size;return s>50&&(t+=.1),s>100&&(t+=.1),Math.min(t,1)}assessContentRelevance(e){let t=["组织","团队","领导","管理","文化","能力","评估","结构化","灵活","直觉","思考","愿景","行动","深思"],s=e.toLowerCase();return Math.min(t.filter(e=>s.includes(e)).length/t.length*2,1)}calculateConfidence(e,t){if(0===e.length)return 0;let s=e.slice(0,t.sourcesUsed.length);return s.reduce((e,t)=>e+t.qualityScore,0)/s.length*.4+s.reduce((e,t)=>e+t.relevanceScore,0)/s.length*.4+.2*Math.min(s.length/3,1)}assessDataQuality(e){return 0===e.length?0:e.reduce((e,t)=>e+t.qualityScore,0)/e.length}extractKeywords(e){let t=e.toLowerCase().replace(/[^\w\s\u4e00-\u9fff]/g," ").split(/\s+/).filter(e=>e.length>2),s=new Map;for(let e of t)s.set(e,(s.get(e)||0)+1);return Array.from(s.entries()).sort((e,t)=>t[1]-e[1]).slice(0,10).map(([e])=>e)}identifyCommonThemes(e){let t=[];return t.push("组织能力评估相关内容"),t}constructor(){this.logger=new i.Yd("DataFusionEngine")}}},2259:(e,t,s)=>{s.d(t,{e:()=>o});var i=s(3966),n=s(5252);n.Ry({model:n.Z_(),messages:n.IX(n.Ry({role:n.Km(["system","user","assistant"]),content:n.Z_()})),temperature:n.Rx().min(0).max(2).optional(),maxTokens:n.Rx().positive().optional(),stream:n.O7().optional()});let r=n.Ry({id:n.Z_(),choices:n.IX(n.Ry({message:n.Ry({role:n.Z_(),content:n.Z_()}),finishReason:n.Z_().optional()})),usage:n.Ry({promptTokens:n.Rx(),completionTokens:n.Rx(),totalTokens:n.Rx()}).optional()});class o{async callMiniMax(e){let t=process.env.MINIMAX_API_KEY;if(!t)throw Error("MiniMax API密钥未配置");return this.makeRequest(`${this.MINIMAX_BASE_URL}/text/chatcompletion`,{...e,model:e.model||"abab6.5-chat"},{Authorization:`Bearer ${t}`,"Content-Type":"application/json"})}async callDeepSeek(e){let t=process.env.DEEPSEEK_API_KEY;if(!t)throw Error("DeepSeek API密钥未配置");return this.makeRequest(`${this.DEEPSEEK_BASE_URL}/chat/completions`,{...e,model:e.model||"deepseek-chat"},{Authorization:`Bearer ${t}`,"Content-Type":"application/json"})}async makeRequest(e,t,s){let i=null;for(let n=1;n<=this.MAX_RETRIES;n++)try{this.logger.debug(`LLM API请求 (尝试 ${n}/${this.MAX_RETRIES})`,{url:e.replace(/\/v1.*/,"/v1/***"),model:t.model});let i=new AbortController,o=setTimeout(()=>i.abort(),this.DEFAULT_TIMEOUT),a=await fetch(e,{method:"POST",headers:s,body:JSON.stringify(t),signal:i.signal});if(clearTimeout(o),!a.ok){let e=await a.text();throw Error(`HTTP ${a.status}: ${e}`)}let l=await a.json(),u=r.parse(l);return this.logger.info("LLM API调用成功",{model:t.model,tokensUsed:u.usage?.totalTokens||0,attempt:n}),u}catch(e){if(i=e,this.logger.warn(`LLM API调用失败 (尝试 ${n}/${this.MAX_RETRIES})`,{error:i.message,model:t.model}),n<this.MAX_RETRIES){let e=1e3*Math.pow(2,n);await new Promise(t=>setTimeout(t,e))}}throw this.logger.error("LLM API调用最终失败",{error:i?.message,attempts:this.MAX_RETRIES}),i||Error("LLM API调用失败")}async healthCheck(){let e={minimax:!1,deepseek:!1};try{await this.callMiniMax({model:"abab6.5-chat",messages:[{role:"user",content:"ping"}],maxTokens:10}),e.minimax=!0}catch(e){this.logger.warn("MiniMax健康检查失败",{error:e})}try{await this.callDeepSeek({model:"deepseek-chat",messages:[{role:"user",content:"ping"}],maxTokens:10}),e.deepseek=!0}catch(e){this.logger.warn("DeepSeek健康检查失败",{error:e})}return e}constructor(){this.logger=new i.Yd("LLMApiClient"),this.MINIMAX_BASE_URL="https://api.minimax.chat/v1",this.DEEPSEEK_BASE_URL="https://api.deepseek.com/v1",this.DEFAULT_TIMEOUT=3e4,this.MAX_RETRIES=3}}},335:(e,t,s)=>{s.d(t,{Z:()=>o});var i=s(3966),n=s(5252);let r=n.Ry({id:n.Z_(),name:n.Z_(),systemPrompt:n.Z_(),userPromptTemplate:n.Z_(),variables:n.IX(n.Z_()),version:n.Z_().optional(),metadata:n.IM(n.Yj()).optional()});class o{constructor(){this.logger=new i.Yd("PromptBuilder"),this.templates=new Map,this.initializeDefaultTemplates()}initializeDefaultTemplates(){this.addTemplate({id:"questionnaire_designer",name:"问卷设计师",systemPrompt:`你是OCTI组织能力评估系统的专业问卷设计师。你的任务是根据用户需求设计高质量的评估问卷。

核心要求：
1. 严格遵循OCTI四维八极理论框架
2. 问题设计要具有科学性和专业性
3. 确保问题的区分度和信效度
4. 输出标准JSON格式的问卷结构

OCTI四维八极框架：
- S/F维度：结构化 ↔ 灵活化
- I/T维度：直觉 ↔ 思考  
- M/V维度：管理 ↔ 愿景
- A/D维度：行动 ↔ 深思

请确保每个维度的问题数量均衡，问题表述清晰准确。`,userPromptTemplate:`请为以下评估需求设计问卷：

评估类型：{{assessmentType}}
目标维度：{{dimensions}}
问卷版本：{{version}}
特殊要求：{{requirements}}

请生成包含以下结构的JSON格式问卷：
{
  "id": "问卷唯一标识",
  "title": "问卷标题",
  "description": "问卷描述",
  "version": "版本信息",
  "questions": [
    {
      "id": "问题ID",
      "text": "问题内容",
      "type": "问题类型",
      "dimension": "所属维度",
      "subdimension": "子维度",
      "options": [选项数组],
      "weight": 权重值
    }
  ],
  "metadata": {
    "estimatedTime": 预估完成时间,
    "totalQuestions": 问题总数,
    "dimensionDistribution": 维度分布统计
  }
}`,variables:["assessmentType","dimensions","version","requirements"]}),this.addTemplate({id:"organization_tutor_standard",name:"组织评估导师-标准版",systemPrompt:`你是OCTI组织能力评估系统的专业分析师。你的任务是基于问卷回答结果，提供深入的组织能力分析和改进建议。

分析框架：
1. OCTI四维八极得分计算
2. 组织能力优势识别
3. 改进机会分析
4. 具体行动建议

输出要求：
- 客观准确的数据分析
- 实用可行的改进建议
- 结构化的报告格式
- 专业的语言表达`,userPromptTemplate:`请分析以下组织评估数据：

组织信息：{{organizationInfo}}
问卷回答：{{responses}}
评估版本：{{version}}

请提供包含以下内容的分析报告：
1. 总体评估得分
2. 四维八极详细分析
3. 组织优势总结
4. 改进建议
5. 下一步行动计划`,variables:["organizationInfo","responses","version"]}),this.logger.info(`已加载 ${this.templates.size} 个默认提示词模板`)}addTemplate(e){let t=r.parse(e);this.templates.set(t.id,t),this.logger.debug(`已添加提示词模板: ${t.id}`)}getTemplate(e){return this.templates.get(e)||null}buildPrompt(e,t){let s=this.getTemplate(e);if(!s)throw Error(`提示词模板不存在: ${e}`);let i=s.variables.filter(e=>!(e in t)||void 0===t[e]);if(i.length>0)throw Error(`缺少必需变量: ${i.join(", ")}`);let n=s.userPromptTemplate;for(let[e,s]of Object.entries(t)){let t=`{{${e}}}`,i="string"==typeof s?s:JSON.stringify(s);n=n.replace(RegExp(t,"g"),i)}return this.logger.debug(`已构建提示词: ${e}`,{variablesUsed:Object.keys(t)}),{systemPrompt:s.systemPrompt,userPrompt:n}}listTemplates(){return Array.from(this.templates.values())}validatePromptSafety(e){let t=[];for(let s of[/ignore\s+previous\s+instructions/i,/system\s*:\s*you\s+are\s+now/i,/forget\s+everything/i,/new\s+instructions/i,/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,/javascript:/i,/on\w+\s*=/i])s.test(e)&&t.push(`检测到潜在的注入攻击模式: ${s.source}`);for(let s of[/api[_-]?key/i,/password/i,/secret/i,/token/i,/credential/i])s.test(e)&&t.push(`检测到可能的敏感信息: ${s.source}`);return{safe:0===t.length,issues:t}}}}};