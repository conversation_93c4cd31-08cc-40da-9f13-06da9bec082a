(()=>{var e={};e.id=786,e.ids=[786],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4462:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>c,routeModule:()=>u,tree:()=>o});var r=t(482),n=t(9108),a=t(2563),i=t.n(a),l=t(8300),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let o=["",{children:["test",{children:["api",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,400)),"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test/api/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,1342)),"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9361,23)),"next/dist/client/components/not-found-error"]}],c=["/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test/api/page.tsx"],x="/test/api/page",m={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/test/api/page",pathname:"/test/api",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},5534:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,2583,23)),Promise.resolve().then(t.t.bind(t,6840,23)),Promise.resolve().then(t.t.bind(t,8771,23)),Promise.resolve().then(t.t.bind(t,3225,23)),Promise.resolve().then(t.t.bind(t,9295,23)),Promise.resolve().then(t.t.bind(t,3982,23))},7154:()=>{},6262:(e,s,t)=>{Promise.resolve().then(t.bind(t,1023))},1023:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var r=t(2295),n=t(3729),a=t(4034),i=t(9956);function l(){let[e,s]=(0,n.useState)(null),[t,l]=(0,n.useState)(null),[d,o]=(0,n.useState)(!1),[c,x]=(0,n.useState)(null),m=async()=>{o(!0),x(null);try{let e=await fetch("/api/test/llm",{method:"POST",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error(`HTTP ${e.status}: ${e.statusText}`);let t=await e.json();s(t)}catch(e){x(e instanceof Error?e.message:"未知错误")}finally{o(!1)}},u=async()=>{o(!0),x(null);try{let e=await fetch("/api/test/llm",{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error(`HTTP ${e.status}: ${e.statusText}`);let s=await e.json();l(s)}catch(e){x(e instanceof Error?e.message:"未知错误")}finally{o(!1)}},p=e=>{switch(e){case"success":return"✅ 成功";case"error":return"❌ 失败";default:return"⏳ 未知"}};return(0,r.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"text-center space-y-2",children:[r.jsx("h1",{className:"text-3xl font-bold",children:"OCTI API 测试中心"}),r.jsx("p",{className:"text-muted-foreground",children:"测试LLM API密钥配置和连接状态"})]}),(0,r.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,r.jsxs)(a.z,{onClick:m,disabled:d,variant:"outline",children:[d?"\uD83D\uDD04 ":"","测试环境变量"]}),(0,r.jsxs)(a.z,{onClick:u,disabled:d,children:[d?"\uD83D\uDD04 ":"","测试LLM连接"]})]}),c&&(0,r.jsxs)(i.Zb,{className:"border-red-200 bg-red-50",children:[r.jsx(i.Ol,{children:r.jsx(i.ll,{className:"text-red-700",children:"❌ 测试失败"})}),r.jsx(i.aY,{children:r.jsx("p",{className:"text-red-600",children:c})})]}),e&&(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[r.jsx(i.ll,{children:"✅ 环境变量检查结果"}),(0,r.jsxs)(i.SZ,{children:["检查时间: ",new Date(e.timestamp).toLocaleString()]})]}),r.jsx(i.aY,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx("h4",{className:"font-medium",children:"MiniMax API"}),r.jsx("div",{className:"text-sm",children:e.environment.minimax_key_exists?"✅ 已配置":"❌ 未配置"}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["预览: ",e.environment.minimax_key_preview]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx("h4",{className:"font-medium",children:"DeepSeek API"}),r.jsx("div",{className:"text-sm",children:e.environment.deepseek_key_exists?"✅ 已配置":"❌ 未配置"}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["预览: ",e.environment.deepseek_key_preview]})]})]})})]}),t&&(0,r.jsxs)(i.Zb,{children:[(0,r.jsxs)(i.Ol,{children:[(0,r.jsxs)(i.ll,{children:[t.success?"✅":"❌"," LLM API 连接测试结果"]}),(0,r.jsxs)(i.SZ,{children:["测试时间: ",new Date(t.timestamp).toLocaleString()]})]}),(0,r.jsxs)(i.aY,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium text-sm",children:"MiniMax密钥长度"}),r.jsx("p",{className:"text-lg font-mono",children:t.environment.minimax_key_length})]}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium text-sm",children:"DeepSeek密钥长度"}),r.jsx("p",{className:"text-lg font-mono",children:t.environment.deepseek_key_length})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)(i.Zb,{children:[r.jsx(i.Ol,{className:"pb-3",children:(0,r.jsxs)(i.ll,{className:"text-lg",children:["MiniMax API - ",p(t.results.minimax.status)]})}),r.jsx(i.aY,{children:t.results.minimax.error?(0,r.jsxs)("div",{className:"text-red-600 text-sm",children:[r.jsx("strong",{children:"错误:"})," ",t.results.minimax.error]}):(0,r.jsxs)("div",{className:"text-green-600 text-sm",children:[r.jsx("strong",{children:"响应:"})," ",t.results.minimax.response||"连接成功"]})})]}),(0,r.jsxs)(i.Zb,{children:[r.jsx(i.Ol,{className:"pb-3",children:(0,r.jsxs)(i.ll,{className:"text-lg",children:["DeepSeek API - ",p(t.results.deepseek.status)]})}),r.jsx(i.aY,{children:t.results.deepseek.error?(0,r.jsxs)("div",{className:"text-red-600 text-sm",children:[r.jsx("strong",{children:"错误:"})," ",t.results.deepseek.error]}):(0,r.jsxs)("div",{className:"text-green-600 text-sm",children:[r.jsx("strong",{children:"响应:"})," ",t.results.deepseek.response||"连接成功"]})})]})]})]})]}),(0,r.jsxs)(i.Zb,{children:[r.jsx(i.Ol,{children:r.jsx(i.ll,{children:"使用说明"})}),(0,r.jsxs)(i.aY,{className:"space-y-2 text-sm text-muted-foreground",children:[(0,r.jsxs)("p",{children:["• ",r.jsx("strong",{children:"测试环境变量"}),": 检查API密钥是否正确配置在环境变量中"]}),(0,r.jsxs)("p",{children:["• ",r.jsx("strong",{children:"测试LLM连接"}),": 实际调用LLM API验证连接和响应"]}),r.jsx("p",{children:"• 确保.env文件中的MINIMAX_API_KEY和DEEPSEEK_API_KEY已正确配置"}),r.jsx("p",{children:"• 测试成功后即可开始使用OCTI智能评估系统的LLM功能"})]})]})]})}},4034:(e,s,t)=>{"use strict";t.d(s,{z:()=>o});var r=t(2295),n=t(3729),a=t.n(n),i=t(1453);let l={default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},d={default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"},o=a().forwardRef(({className:e,variant:s="default",size:t="default",loading:n=!1,disabled:a,children:o,...c},x)=>(0,r.jsxs)("button",{className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",l[s],d[t],e),ref:x,disabled:a||n,...c,children:[n&&(0,r.jsxs)("svg",{className:"mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[r.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),r.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),o]}));o.displayName="Button"},9956:(e,s,t)=>{"use strict";t.d(s,{Ol:()=>o,SZ:()=>x,Zb:()=>d,aY:()=>m,ll:()=>c});var r=t(2295),n=t(3729),a=t.n(n),i=t(1453);let l={default:"bg-card text-card-foreground",outlined:"bg-card text-card-foreground border border-border",elevated:"bg-card text-card-foreground shadow-lg"},d=a().forwardRef(({className:e,variant:s="default",...t},n)=>r.jsx("div",{ref:n,className:(0,i.cn)("rounded-lg border shadow-sm",l[s],e),...t}));d.displayName="Card";let o=a().forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...s}));o.displayName="CardHeader";let c=a().forwardRef(({className:e,...s},t)=>r.jsx("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));c.displayName="CardTitle";let x=a().forwardRef(({className:e,...s},t)=>r.jsx("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));x.displayName="CardDescription";let m=a().forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...s}));m.displayName="CardContent",a().forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},1453:(e,s,t)=>{"use strict";t.d(s,{cn:()=>a});var r=t(6553),n=t(9976);function a(...e){return(0,n.m6)((0,r.W)(e))}},1342:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l,metadata:()=>i});var r=t(5036),n=t(2195),a=t.n(n);t(5023);let i={title:"OCTI智能评估系统",description:"基于OCTI四维八极理论的智能组织评估平台",keywords:["OCTI","组织评估","智能评估","四维八极"],authors:[{name:"OCTI Team"}],viewport:"width=device-width, initial-scale=1",robots:"index, follow",openGraph:{title:"OCTI智能评估系统",description:"基于OCTI四维八极理论的智能组织评估平台",type:"website",locale:"zh_CN"}};function l({children:e}){return r.jsx("html",{lang:"zh-CN",className:"h-full",children:r.jsx("body",{className:`${a().className} h-full antialiased`,children:r.jsx("div",{id:"root",className:"min-h-full",children:e})})})}},400:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>a,__esModule:()=>n,default:()=>i});let r=(0,t(6843).createProxy)(String.raw`/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/test/api/page.tsx`),{__esModule:n,$$typeof:a}=r,i=r.default},5023:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,883,844],()=>t(4462));module.exports=r})();