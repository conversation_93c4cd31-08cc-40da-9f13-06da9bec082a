"use strict";(()=>{var e={};e.id=468,e.ids=[468],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6950:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>d,originalPathname:()=>h,patchFetch:()=>E,requestAsyncStorage:()=>u,routeModule:()=>p,serverHooks:()=>m,staticGenerationAsyncStorage:()=>l,staticGenerationBailout:()=>_});var s={};r.r(s),r.d(s,{GET:()=>i,POST:()=>c});var o=r(5419),n=r(9108),a=r(9678);async function i(e){try{let e={minimax:{status:"unknown",error:null},deepseek:{status:"unknown",error:null}};try{let t=await fetch("https://api.minimax.chat/v1/text/chatcompletion",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${process.env.MINIMAX_API_KEY}`},body:JSON.stringify({model:"abab6.5s-chat",messages:[{role:"user",content:'你好，这是一个API连接测试。请简单回复"连接成功"。'}],max_tokens:50,temperature:.1})});if(t.ok){let r=await t.json();e.minimax.status="success",e.minimax.response=r.choices?.[0]?.message?.content||"响应为空"}else{let r=await t.text();e.minimax.status="error",e.minimax.error=`HTTP ${t.status}: ${r}`}}catch(t){e.minimax.status="error",e.minimax.error=t instanceof Error?t.message:"未知错误"}try{let t=await fetch("https://api.deepseek.com/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${process.env.DEEPSEEK_API_KEY}`},body:JSON.stringify({model:"deepseek-chat",messages:[{role:"user",content:'你好，这是一个API连接测试。请简单回复"连接成功"。'}],max_tokens:50,temperature:.1})});if(t.ok){let r=await t.json();e.deepseek.status="success",e.deepseek.response=r.choices?.[0]?.message?.content||"响应为空"}else{let r=await t.text();e.deepseek.status="error",e.deepseek.error=`HTTP ${t.status}: ${r}`}}catch(t){e.deepseek.status="error",e.deepseek.error=t instanceof Error?t.message:"未知错误"}let t={minimax_key_exists:!!process.env.MINIMAX_API_KEY,deepseek_key_exists:!!process.env.DEEPSEEK_API_KEY,minimax_key_length:process.env.MINIMAX_API_KEY?.length||0,deepseek_key_length:process.env.DEEPSEEK_API_KEY?.length||0};return Object(function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}())({environment:t,results:e})}catch(e){return console.error("LLM测试API错误:",e),Object(function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}())("LLM连接测试失败")}}async function c(e){try{let e={minimax_configured:!!process.env.MINIMAX_API_KEY,deepseek_configured:!!process.env.DEEPSEEK_API_KEY};return Object(function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}())({message:"API密钥环境变量检查完成",environment:e})}catch(e){return Object(function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}())("环境变量检查失败")}}!function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}();let p=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/test/llm/route",pathname:"/api/test/llm",filename:"route",bundlePath:"app/api/test/llm/route"},resolvedPagePath:"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/test/llm/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:u,staticGenerationAsyncStorage:l,serverHooks:m,headerHooks:d,staticGenerationBailout:_}=p,h="/api/test/llm/route";function E(){return(0,a.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:l})}},5419:(e,t,r)=>{e.exports=r(517)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638],()=>r(6950));module.exports=s})();