"use strict";(()=>{var e={};e.id=657,e.ids=[657],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},7811:(e,t,s)=>{s.r(t),s.d(t,{headerHooks:()=>_,originalPathname:()=>A,patchFetch:()=>S,requestAsyncStorage:()=>y,routeModule:()=>E,serverHooks:()=>I,staticGenerationAsyncStorage:()=>D,staticGenerationBailout:()=>C});var a={};s.r(a),s.d(a,{GET:()=>v,OPTIONS:()=>w,POST:()=>O});var n=s(5419),r=s(9108),o=s(9678),i=s(8070),c=s(5252),d=s(2178),u=s(4100),l=s(3966);(function(){var e=Error("Cannot find module '../../../../services/llm/LLMApiClient'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '../../../../services/llm/PromptBuilder'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '../../../../services/data/DataFusionEngine'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}();let m=new l.Yd("AssessmentEvaluateAPI"),p=c.Ry({assessmentId:c.Z_().min(1,"评估ID不能为空"),responses:c.IX(c.Ry({questionId:c.Z_(),answer:c.Yj()})).min(1,"至少需要一个回答"),version:c.Km(["standard","professional"]).default("standard"),options:c.Ry({includeRecommendations:c.O7().default(!0),includeBenchmarking:c.O7().default(!1),dataFusion:c.Ry({uploadedFiles:c.IX(c.Z_()).optional(),webData:c.IX(c.Z_()).optional()}).optional()}).optional()});async function h(e){let t=await e.json();return p.parse(t)}async function g(e){let{organizationTutorAgent:t}=f();return await t.generateAssessment(e.responses,{version:e.version,assessmentId:e.assessmentId,...e.options})}function f(){let e=Object(function(){var e=Error("Cannot find module '../../../../services/llm/LLMApiClient'");throw e.code="MODULE_NOT_FOUND",e}())({minimax:{apiKey:process.env.MINIMAX_API_KEY,baseUrl:"https://api.minimax.chat/v1",model:"abab6.5s-chat"},deepseek:{apiKey:process.env.DEEPSEEK_API_KEY,baseUrl:"https://api.deepseek.com/v1",model:"deepseek-chat"}}),t=Object(function(){var e=Error("Cannot find module '../../../../services/llm/PromptBuilder'");throw e.code="MODULE_NOT_FOUND",e}())("/configs"),s=Object(function(){var e=Error("Cannot find module '../../../../services/data/DataFusionEngine'");throw e.code="MODULE_NOT_FOUND",e}())({enabled:!0,strategy:"weighted",maxDataLength:1e4,minConfidence:.6,weightThreshold:.3,qualityThreshold:.7,dataSources:[]}),a=new u.S(e,t,s);return{llmClient:e,promptBuilder:t,dataFusionEngine:s,organizationTutorAgent:a}}async function O(e){try{let t=await h(e),s=await g(t);return Object(function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}())({assessmentId:s.assessmentId,report:s.report,generatedAt:new Date().toISOString()},201)}catch(e){if(e instanceof d.jm)return Object(function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}())(e);return console.error("评估处理失败:",e),Object(function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}())("评估处理失败")}}async function v(e){try{let{searchParams:t}=new URL(e.url),s=t.get("assessmentId");if(!s)return i.Z.json({success:!1,error:{code:"MISSING_PARAMETER",message:"Assessment ID is required"}},{status:400});let a=AssessmentIdSchema.parse({assessmentId:s});m.info("Retrieving assessment report",{assessmentId:a.assessmentId});let{organizationTutorAgent:n}=f();return i.Z.json({success:!1,error:{code:"NOT_FOUND",message:`No assessment found with ID: ${a.assessmentId}. Please generate a new assessment.`}},{status:404})}catch(e){if(m.error("Failed to retrieve assessment report",{error:e}),e instanceof d.jm)return i.Z.json({success:!1,error:{code:"VALIDATION_ERROR",message:"Invalid assessment ID",details:e.errors}},{status:400});return i.Z.json({success:!1,error:{code:"INTERNAL_ERROR",message:"Failed to retrieve assessment report"}},{status:500})}}async function w(){try{m.info("Retrieving assessment statistics");let{organizationTutorAgent:e}=f(),t=e.getCacheStats();return i.Z.json({success:!0,data:{statistics:{totalAssessments:t.size,cacheHitRate:t.hitRate,oldestEntry:t.oldestEntry,lastUpdated:new Date().toISOString()},capabilities:{supportedVersions:["standard","professional"],maxResponses:60,supportedDataFusion:!0,supportedBenchmarking:!0}}})}catch(e){return m.error("Failed to retrieve assessment statistics",{error:e}),i.Z.json({success:!1,error:{code:"INTERNAL_ERROR",message:"Failed to retrieve statistics"}},{status:500})}}let E=new n.AppRouteRouteModule({definition:{kind:r.x.APP_ROUTE,page:"/api/assessment/evaluate/route",pathname:"/api/assessment/evaluate",filename:"route",bundlePath:"app/api/assessment/evaluate/route"},resolvedPagePath:"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/assessment/evaluate/route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:y,staticGenerationAsyncStorage:D,serverHooks:I,headerHooks:_,staticGenerationBailout:C}=E,A="/api/assessment/evaluate/route";function S(){return(0,o.patchFetch)({serverHooks:I,staticGenerationAsyncStorage:D})}},3966:(e,t,s)=>{s.d(t,{Yd:()=>a});class a{constructor(e="App"){this.context=e}formatMessage(e,t,s){let a=new Date().toISOString(),n=s?` ${JSON.stringify(s)}`:"";return`[${a}] [${e.toUpperCase()}] [${this.context}] ${t}${n}`}debug(e,t){"debug"===process.env.LOG_LEVEL&&console.debug(this.formatMessage("debug",e,t))}info(e,t){console.info(this.formatMessage("info",e,t))}warn(e,t){console.warn(this.formatMessage("warn",e,t))}error(e,t){console.error(this.formatMessage("error",e,t))}setContext(e){this.context=e}}new a("OCTI")},4100:(e,t,s)=>{s.d(t,{S:()=>n});let a=new(s(3966)).Yd("OrganizationTutorAgent");class n{constructor(e,t,s){this.resultCache=new Map,this.cacheExpiry=864e5,this.isInitialized=!1,this.llmClient=e,this.promptBuilder=t,this.dataFusionEngine=s}async initialize(){if(this.isInitialized){a.warn("OrganizationTutorAgent 已经初始化");return}try{if(!this.llmClient)throw Error("LLMApiClient 是必需的依赖项");if(!this.promptBuilder)throw Error("PromptBuilder 是必需的依赖项");if(!this.dataFusionEngine)throw Error("DataFusionEngine 是必需的依赖项");this.cleanExpiredCache(),this.isInitialized=!0,a.info("OrganizationTutorAgent 初始化完成")}catch(e){throw a.error("OrganizationTutorAgent 初始化失败",{error:e}),e}}async generateAssessment(e,t={version:"standard",analysisMode:"basic",includeRecommendations:!0,outputLanguage:"zh"}){if(!this.isInitialized)throw Error("OrganizationTutorAgent 未初始化");if(!e||!e.organizationId)throw Error("评估数据无效：缺少组织ID");if(!e.responses||0===e.responses.length)throw Error("评估数据无效：缺少响应数据");let s=Date.now();try{let n;let r=this.generateCacheKey(e,t),o=this.resultCache.get(r);if(o&&Date.now()-o.timestamp<this.cacheExpiry)return a.info("使用缓存的评估结果",{cacheKey:r}),o.data;let i=this.preprocessAssessmentData(e),c=null;t.externalData&&t.externalData.length>0&&(c=await this.dataFusionEngine.fuseData(t.externalData.map(e=>({sourceId:e.source,content:JSON.stringify(e.data),contentType:e.type,metadata:{timestamp:e.timestamp,reliability:e.reliability}})),t.dataFusion));let d={version:t.version,agentType:"organization_tutor",context:{assessmentData:i,analysisMode:t.analysisMode,includeRecommendations:t.includeRecommendations,customFocus:t.customFocus,outputLanguage:t.outputLanguage,organizationType:e.metadata.organizationType,industryContext:e.metadata.industryContext},externalData:c?[{source:"external_data_fusion",content:JSON.stringify(c),type:"fusion_result",weight:1}]:void 0,dataFusion:t.dataFusion},u=await this.promptBuilder.buildPrompt(d);n="professional"===t.version?(await this.llmClient.dualModelChat({messages:[{role:"system",content:u.systemPrompt},{role:"user",content:u.userPrompt}],temperature:.3,maxTokens:8e3},"sequential")).secondary:await this.llmClient.chat("minimax",{messages:[{role:"system",content:u.systemPrompt},{role:"user",content:u.userPrompt}],temperature:.3,maxTokens:4e3});let l=await this.parseAssessmentResponse(n.content,e,t);this.resultCache.set(r,{data:l,timestamp:Date.now()});let m=Date.now()-s;return a.info("评估报告生成完成",{organizationId:e.organizationId,version:t.version,processingTime:m}),l}catch(s){throw a.error("评估报告生成失败",{error:s instanceof Error?s.message:String(s),organizationId:e.organizationId,options:t}),Error(`评估报告生成失败: ${s instanceof Error?s.message:"未知错误"}`)}}generateCacheKey(e,t){return[e.organizationId,t.version,t.analysisMode,JSON.stringify(t.customFocus||[]),e.metadata.completedAt].join("_").replace(/[^a-zA-Z0-9_]/g,"_")}preprocessAssessmentData(e){return{organizationId:e.organizationId,responses:e.responses.map(e=>({questionId:e.questionId,answer:e.answer,dimension:e.dimension,subdimension:e.subdimension})),metadata:e.metadata}}async parseAssessmentResponse(e,t,s){try{let a;let n=e.match(/```json\s*([\s\S]*?)\s*```/)||e.match(/\{[\s\S]*\}/);return a=n?JSON.parse(n[1]||n[0]):this.parseStructuredReport(e),{id:`report_${Date.now()}`,organizationId:t.organizationId,version:s.version,overallScore:a.overallScore||0,dimensionScores:a.dimensionScores||{},strengths:a.strengths||[],improvements:a.improvements||[],recommendations:a.recommendations||[],nextSteps:a.nextSteps||[],metadata:{generatedAt:new Date,analysisMode:s.analysisMode,dataSourcesUsed:s.externalData?.map(e=>e.source)||[]}}}catch(t){throw a.error("评估响应解析失败",{error:t,content:e.substring(0,500)}),Error("评估响应解析失败")}}parseStructuredReport(e){return{overallScore:75,dimensionScores:{},strengths:[],improvements:[],recommendations:[],nextSteps:[]}}cleanExpiredCache(){let e=Date.now();for(let[t,s]of this.resultCache.entries())e-s.timestamp>this.cacheExpiry&&this.resultCache.delete(t)}getCacheStats(){let e=Array.from(this.resultCache.values()),t=e.length>0?e.reduce((e,t)=>new Date(t.timestamp)<new Date(e.timestamp)?t:e).timestamp:null;return{size:this.resultCache.size,hitRate:0,oldestEntry:t}}clearCache(){this.resultCache.clear(),a.info("Assessment cache cleared")}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[638,206,252],()=>s(7811));module.exports=a})();