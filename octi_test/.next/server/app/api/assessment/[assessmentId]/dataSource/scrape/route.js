"use strict";(()=>{var e={};e.id=194,e.ids=[194],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},1493:(e,r,s)=>{s.r(r),s.d(r,{headerHooks:()=>h,originalPathname:()=>D,patchFetch:()=>S,requestAsyncStorage:()=>m,routeModule:()=>l,serverHooks:()=>f,staticGenerationAsyncStorage:()=>O,staticGenerationBailout:()=>_});var t={};s.r(t),s.d(t,{POST:()=>p});var a=s(5419),o=s(9108),n=s(9678),u=s(8070),i=s(5252),c=s(2178);!function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}();let d=i.Ry({url:i.Z_().url("请提供有效的URL地址")});async function p(e,{params:r}){try{let{assessmentId:s}=r,t=await e.json(),a=d.parse(t);if(!s||s.length<1)return u.Z.json({success:!1,error:{code:"INVALID_ASSESSMENT_ID",message:"无效的评估ID"}},{status:400});let o=`ds-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;return Object(function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}())({dataSourceId:o,url:a.url,message:"URL已接收，正在排队进行数据采集"},202)}catch(e){if(e instanceof c.jm)return Object(function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}())(e);return console.error("网络数据采集请求失败:",e),Object(function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}())("数据采集请求失败")}}let l=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/assessment/[assessmentId]/dataSource/scrape/route",pathname:"/api/assessment/[assessmentId]/dataSource/scrape",filename:"route",bundlePath:"app/api/assessment/[assessmentId]/dataSource/scrape/route"},resolvedPagePath:"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/assessment/[assessmentId]/dataSource/scrape/route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:m,staticGenerationAsyncStorage:O,serverHooks:f,headerHooks:h,staticGenerationBailout:_}=l,D="/api/assessment/[assessmentId]/dataSource/scrape/route";function S(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:O})}}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,206,252],()=>s(1493));module.exports=t})();