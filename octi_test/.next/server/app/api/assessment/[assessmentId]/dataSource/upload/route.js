"use strict";(()=>{var e={};e.id=713,e.ids=[713],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},1017:e=>{e.exports=require("path")},5785:(e,s,t)=>{t.r(s),t.d(s,{headerHooks:()=>h,originalPathname:()=>D,patchFetch:()=>_,requestAsyncStorage:()=>m,routeModule:()=>l,serverHooks:()=>O,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>g});var r={};t.r(r),t.d(r,{POST:()=>p});var a=t(5419),o=t(9108),n=t(9678),i=t(8070);let u=require("fs/promises");var c=t(1017);!function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}();let d=["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","text/plain"];async function p(e,{params:s}){try{let{assessmentId:t}=s;if(!t||t.length<1)return i.Z.json({success:!1,error:{code:"INVALID_ASSESSMENT_ID",message:"无效的评估ID"}},{status:400});let r=(await e.formData()).get("file");if(!r)return i.Z.json({success:!1,error:{code:"NO_FILE",message:"请选择要上传的文件"}},{status:400});if(!d.includes(r.type))return i.Z.json({success:!1,error:{code:"INVALID_TYPE",message:"不支持的文件类型，仅支持 PDF、DOCX、TXT 格式"}},{status:400});if(r.size>52428800)return i.Z.json({success:!1,error:{code:"FILE_TOO_LARGE",message:"文件大小超过 50MB 限制"}},{status:400});let a=`ds-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,o=`${a}-${r.name}`,n=(0,c.join)(process.cwd(),"uploads",t);await (0,u.mkdir)(n,{recursive:!0});let p=(0,c.join)(n,o),l=await r.arrayBuffer(),m=Buffer.from(l);return await (0,u.writeFile)(p,m),Object(function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}())({dataSourceId:a,fileName:r.name,fileSize:r.size,fileType:r.type,message:"文件已接收，正在排队处理"},202)}catch(e){return console.error("文件上传失败:",e),Object(function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}())("文件上传失败")}}let l=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/assessment/[assessmentId]/dataSource/upload/route",pathname:"/api/assessment/[assessmentId]/dataSource/upload",filename:"route",bundlePath:"app/api/assessment/[assessmentId]/dataSource/upload/route"},resolvedPagePath:"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/assessment/[assessmentId]/dataSource/upload/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:m,staticGenerationAsyncStorage:f,serverHooks:O,headerHooks:h,staticGenerationBailout:g}=l,D="/api/assessment/[assessmentId]/dataSource/upload/route";function _(){return(0,n.patchFetch)({serverHooks:O,staticGenerationAsyncStorage:f})}}};var s=require("../../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,206],()=>t(5785));module.exports=r})();