"use strict";(()=>{var e={};e.id=388,e.ids=[388],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5088:(e,t,s)=>{s.r(t),s.d(t,{headerHooks:()=>E,originalPathname:()=>U,patchFetch:()=>b,requestAsyncStorage:()=>_,routeModule:()=>w,serverHooks:()=>v,staticGenerationAsyncStorage:()=>j,staticGenerationBailout:()=>D});var n={};s.r(n),s.d(n,{GET:()=>m,POST:()=>O,PUT:()=>h});var r=s(5419),a=s(9108),o=s(9678),i=s(8070),u=s(342),c=s(5252),d=s(2178);!function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}();let l=null;async function p(){return l||(l=u.s.getInstance(),await l.initialize()),l}async function m(e){try{let{searchParams:t}=new URL(e.url),s=t.get("id"),n=await p();if(s){let e=await n.handleAssessmentRequest({action:"get",assessmentId:s});if(e.success)return i.Z.json(e,{status:200});return i.Z.json(e,{status:404})}{let e={success:!0,data:[{id:"assessment_1",title:"OCTI智能评估 - 团队协作",status:"active",createdAt:new Date().toISOString()},{id:"assessment_2",title:"OCTI智能评估 - 领导力",status:"completed",createdAt:new Date().toISOString()}]};return i.Z.json(e,{status:200})}}catch(e){return console.error("评估API GET请求失败:",e),i.Z.json({success:!1,error:"服务器内部错误"},{status:500})}}let f=c.Ry({title:c.Z_().min(1,"标题不能为空").max(100,"标题不能超过100字符"),description:c.Z_().optional(),type:c.Km(["team_collaboration","leadership","innovation"],{errorMap:()=>({message:"无效的评估类型"})}),dimensions:c.IX(c.Z_()).min(1,"至少需要一个维度").max(10,"维度不能超过10个"),requirements:c.Ry({minQuestions:c.Rx().min(5).max(100).optional(),maxDuration:c.Rx().min(10).max(120).optional(),version:c.Km(["standard","professional"]).default("standard")}).optional()});async function O(e){try{let t=await e.json(),s=f.parse(t),n=await p(),r=await n.handleAssessmentRequest({action:"create",data:s});if(r.success)return Object(function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}())(r.data,201);return Object(function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}())("CREATE_FAILED",r.error||"创建评估失败")}catch(e){if(e instanceof d.jm)return Object(function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}())(e);return console.error("创建评估失败:",e),Object(function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}())()}}async function h(e){try{let{assessmentId:t,answers:s}=await e.json();if(!t||!s)return i.Z.json({success:!1,error:"缺少必要参数: assessmentId 或 answers"},{status:400});let n=await p(),r=await n.handleAssessmentRequest({action:"submit",assessmentId:t,data:{answers:s}});if(r.success)return i.Z.json(r,{status:200});return i.Z.json(r,{status:400})}catch(e){return console.error("提交评估失败:",e),i.Z.json({success:!1,error:"服务器内部错误"},{status:500})}}let w=new r.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/assessments/route",pathname:"/api/assessments",filename:"route",bundlePath:"app/api/assessments/route"},resolvedPagePath:"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/assessments/route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:_,staticGenerationAsyncStorage:j,serverHooks:v,headerHooks:E,staticGenerationBailout:D}=w,U="/api/assessments/route";function b(){return(0,o.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:j})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),n=t.X(0,[638,206,252,447,342],()=>s(5088));module.exports=n})();