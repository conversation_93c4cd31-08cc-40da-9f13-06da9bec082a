"use strict";(()=>{var e={};e.id=868,e.ids=[868],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4493:(e,s,t)=>{t.r(s),t.d(s,{headerHooks:()=>f,originalPathname:()=>g,patchFetch:()=>j,requestAsyncStorage:()=>m,routeModule:()=>d,serverHooks:()=>h,staticGenerationAsyncStorage:()=>y,staticGenerationBailout:()=>v});var a={};t.r(a),t.d(a,{POST:()=>p});var r=t(5419),n=t(9108),o=t(9678),i=t(8070),u=t(342);let c=null;async function l(){return c||(c=u.s.getInstance(),await c.initialize()),c}async function p(e,{params:s}){try{let e=s.id;if(!e)return i.Z.json({success:!1,error:"缺少评估ID"},{status:400});let t=await l(),a=await t.handleAssessmentRequest({action:"analyze",assessmentId:e});if(a.success)return i.Z.json(a,{status:200});return i.Z.json(a,{status:400})}catch(e){return console.error("评估分析失败:",e),i.Z.json({success:!1,error:"服务器内部错误"},{status:500})}}let d=new r.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/assessments/[id]/analyze/route",pathname:"/api/assessments/[id]/analyze",filename:"route",bundlePath:"app/api/assessments/[id]/analyze/route"},resolvedPagePath:"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/assessments/[id]/analyze/route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:m,staticGenerationAsyncStorage:y,serverHooks:h,headerHooks:f,staticGenerationBailout:v}=d,g="/api/assessments/[id]/analyze/route";function j(){return(0,o.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:y})}}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[638,206,252,447,342],()=>t(4493));module.exports=a})();