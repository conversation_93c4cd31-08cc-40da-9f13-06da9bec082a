"use strict";(()=>{var e={};e.id=600,e.ids=[600],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},8515:(e,t,s)=>{s.r(t),s.d(t,{headerHooks:()=>g,originalPathname:()=>f,patchFetch:()=>P,requestAsyncStorage:()=>m,routeModule:()=>d,serverHooks:()=>h,staticGenerationAsyncStorage:()=>y,staticGenerationBailout:()=>v});var r={};s.r(r),s.d(r,{GET:()=>l});var a=s(5419),n=s(9108),o=s(9678),u=s(8070),i=s(342);let c=null;async function p(){return c||(c=i.s.getInstance(),await c.initialize()),c}async function l(e){try{let{searchParams:t}=new URL(e.url),s=t.get("action")||"status",r=await p(),a=await r.handleSystemRequest({action:s});if(a.success)return u.Z.json(a,{status:200});return u.Z.json(a,{status:400})}catch(e){return console.error("系统API请求失败:",e),u.Z.json({success:!1,error:"服务器内部错误"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/system/route",pathname:"/api/system",filename:"route",bundlePath:"app/api/system/route"},resolvedPagePath:"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/system/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:m,staticGenerationAsyncStorage:y,serverHooks:h,headerHooks:g,staticGenerationBailout:v}=d,f="/api/system/route";function P(){return(0,o.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:y})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,206,252,447,342],()=>s(8515));module.exports=r})();