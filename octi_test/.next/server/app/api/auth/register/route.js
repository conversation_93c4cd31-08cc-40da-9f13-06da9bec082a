"use strict";(()=>{var e={};e.id=2,e.ids=[2],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},8870:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>_,originalPathname:()=>E,patchFetch:()=>U,requestAsyncStorage:()=>m,routeModule:()=>p,serverHooks:()=>f,staticGenerationAsyncStorage:()=>O,staticGenerationBailout:()=>h});var o={};t.r(o),t.d(o,{POST:()=>l});var a=t(5419),n=t(9108),i=t(9678),s=t(8070),u=t(5252),c=t(2178);(function(){var e=Error("Cannot find module 'bcryptjs'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}();let d=u.Ry({email:u.Z_().email("邮箱格式无效"),password:u.Z_().min(8,"密码至少8位").max(100,"密码不能超过100位"),name:u.Z_().min(1,"姓名不能为空").max(50,"姓名不能超过50字符"),organizationName:u.Z_().optional(),role:u.Km(["USER","ADMIN"]).default("USER")});async function l(e){try{let r=await e.json(),t=d.parse(r);if(await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.findUnique({where:{email:t.email}}))return s.Z.json({success:!1,error:{code:"EMAIL_EXISTS",message:"该邮箱已被注册"}},{status:400});let o=await Object(function(){var e=Error("Cannot find module 'bcryptjs'");throw e.code="MODULE_NOT_FOUND",e}())(t.password,12),a=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.create({data:{email:t.email,password:o,name:t.name,role:t.role},select:{id:!0,email:!0,name:!0,role:!0,createdAt:!0}});return Object(function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}())({user:a,message:"用户注册成功"},201)}catch(e){if(e instanceof c.jm)return Object(function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}())(e);return console.error("用户注册失败:",e),Object(function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}())("注册失败，请稍后重试")}}let p=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:"app/api/auth/register/route"},resolvedPagePath:"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/auth/register/route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:m,staticGenerationAsyncStorage:O,serverHooks:f,headerHooks:_,staticGenerationBailout:h}=p,E="/api/auth/register/route";function U(){return(0,i.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:O})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[638,206,252],()=>t(8870));module.exports=o})();