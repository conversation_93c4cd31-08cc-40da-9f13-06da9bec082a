"use strict";(()=>{var e={};e.id=873,e.ids=[873],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6205:(e,r,o)=>{o.r(r),o.d(r,{headerHooks:()=>_,originalPathname:()=>E,patchFetch:()=>D,requestAsyncStorage:()=>O,routeModule:()=>p,serverHooks:()=>f,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>h});var t={};o.r(t),o.d(t,{POST:()=>l});var n=o(5419),a=o(9108),i=o(9678),s=o(8070),u=o(5252),d=o(2178);(function(){var e=Error("Cannot find module 'bcryptjs'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module 'jsonwebtoken'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}();let c=u.Ry({email:u.Z_().email("邮箱格式无效"),password:u.Z_().min(1,"密码不能为空")});async function l(e){try{let r=await e.json(),o=c.parse(r),t=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.findUnique({where:{email:o.email}});if(!t||!await Object(function(){var e=Error("Cannot find module 'bcryptjs'");throw e.code="MODULE_NOT_FOUND",e}())(o.password,t.password))return s.Z.json({success:!1,error:{code:"INVALID_CREDENTIALS",message:"邮箱或密码错误"}},{status:401});let n=Object(function(){var e=Error("Cannot find module 'jsonwebtoken'");throw e.code="MODULE_NOT_FOUND",e}())({sub:t.id,email:t.email,role:t.role},process.env.JWT_SECRET,{expiresIn:"7d"});return Object(function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}())({token:n,user:{id:t.id,email:t.email,name:t.name,role:t.role}})}catch(e){if(e instanceof d.jm)return Object(function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}())(e);return console.error("用户登录失败:",e),Object(function(){var e=Error("Cannot find module '@/lib/api-response'");throw e.code="MODULE_NOT_FOUND",e}())("登录失败，请稍后重试")}}let p=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/auth/login/route",pathname:"/api/auth/login",filename:"route",bundlePath:"app/api/auth/login/route"},resolvedPagePath:"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/auth/login/route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:O,staticGenerationAsyncStorage:m,serverHooks:f,headerHooks:_,staticGenerationBailout:h}=p,E="/api/auth/login/route";function D(){return(0,i.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:m})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var o=e=>r(r.s=e),t=r.X(0,[638,206,252],()=>o(6205));module.exports=t})();