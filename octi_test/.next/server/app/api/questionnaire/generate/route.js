"use strict";(()=>{var e={};e.id=577,e.ids=[577],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3623:(e,t,n)=>{n.r(t),n.d(t,{headerHooks:()=>R,originalPathname:()=>C,patchFetch:()=>S,requestAsyncStorage:()=>D,routeModule:()=>_,serverHooks:()=>Z,staticGenerationAsyncStorage:()=>T,staticGenerationBailout:()=>j});var i={};n.r(i),n.d(i,{GET:()=>v,OPTIONS:()=>w,POST:()=>q});var s=n(5419),a=n(9108),o=n(9678),r=n(8070),u=n(3966),d=n(3686),c=n(2259),m=n(335),l=n(3320),p=n(5252),g=n(2178);let y=new u.Yd("QuestionnaireAPI"),f=p.Ry({version:p.Km(["standard","professional"]).default("standard"),organizationType:p.Z_().optional(),industryContext:p.Z_().optional(),targetAudience:p.Z_().optional(),customRequirements:p.Z_().optional(),questionCount:p.Rx().min(10).max(100).optional(),focusDimensions:p.IX(p.Z_()).optional(),externalData:p.IX(p.Ry({source:p.Z_(),content:p.Z_(),type:p.Z_(),weight:p.Rx().min(0).max(1).optional()})).optional(),dataFusion:p.Ry({enabled:p.O7(),strategy:p.Km(["weighted","priority","consensus"]).default("weighted"),maxDataLength:p.Rx().default(1e4),minConfidence:p.Rx().min(0).max(1).default(.7),weightThreshold:p.Rx().min(0).max(1).default(.1),qualityThreshold:p.Rx().min(0).max(1).default(.8),dataSources:p.IX(p.Ry({id:p.Z_(),type:p.Km(["file","url","database","api"]),source:p.Z_(),weight:p.Rx().min(0).max(1),processingMethod:p.Km(["summary","extraction","analysis","classification"]),enabled:p.O7().default(!0)})).default([])}).optional()}),h=null;function x(){if(!h){let e=new c.e,t=new m.Z,n=new l.e({enabled:!1,strategy:"weighted",maxDataLength:1e4,minConfidence:.7,weightThreshold:.1,qualityThreshold:.8,dataSources:[]});h=new d.o(e,t,n)}return{questionDesignerAgent:h}}async function q(e){try{y.info("Questionnaire generation request received");let t=await e.json(),n=f.parse(t);y.info("Request validated",{version:n.version,organizationType:n.organizationType,hasExternalData:!!n.externalData?.length});let{questionDesignerAgent:i}=x(),s=await i.designQuestionnaire({version:n.version,organizationType:n.organizationType,industryContext:n.industryContext,targetAudience:n.targetAudience,customRequirements:n.customRequirements,questionCount:n.questionCount,focusDimensions:n.focusDimensions,externalData:n.externalData?n.externalData.map(e=>({sourceId:e.source,content:e.content,contentType:e.type,timestamp:new Date,metadata:{weight:e.weight||1}})):void 0,dataFusion:n.dataFusion?{enabled:n.dataFusion.enabled,fusionStrategy:n.dataFusion.strategy,maxDataLength:n.dataFusion.maxDataLength,dataSources:n.dataFusion.dataSources.map(e=>({type:e.type,source:e.source,weight:e.weight,processingMethod:e.processingMethod}))}:void 0});return y.info("Questionnaire generated successfully",{questionnaireId:s.id,questionCount:s.questions.length,version:s.version}),r.Z.json({success:!0,data:{questionnaire:{id:s.id,title:s.title,description:s.description,version:s.version,questions:s.questions.map(e=>({id:e.id,text:e.text,type:e.type,dimension:e.dimension,subdimension:e.subdimension,options:e.options,required:!0,weight:e.weight})),metadata:{createdAt:new Date().toISOString(),estimatedTime:2*s.questions.length,difficulty:"professional"===s.version?"high":"medium",tags:[s.version,"octi-framework"]}},statistics:{totalQuestions:s.questions.length,dimensionDistribution:s.questions.reduce((e,t)=>(e[t.dimension]=(e[t.dimension]||0)+1,e),{}),typeDistribution:s.questions.reduce((e,t)=>(e[t.type]=(e[t.type]||0)+1,e),{})}},metadata:{timestamp:new Date().toISOString(),version:n.version,processingTime:Date.now()-Date.now()}})}catch(e){if(y.error("Questionnaire generation failed",{error:e}),e instanceof g.jm)return r.Z.json({success:!1,error:{type:"validation_error",message:"Invalid request data",details:e.errors.map(e=>({field:e.path.join("."),message:e.message,code:e.code}))}},{status:400});if(e instanceof Error)return r.Z.json({success:!1,error:{type:"generation_error",message:e.message}},{status:500});return r.Z.json({success:!1,error:{type:"unknown_error",message:"An unexpected error occurred"}},{status:500})}}async function v(e){try{let{searchParams:t}=new URL(e.url),n=t.get("id");if(!n)return r.Z.json({success:!1,error:{type:"validation_error",message:"Questionnaire ID is required"}},{status:400});let{questionDesignerAgent:i}=x(),s=await i.getQuestionnairePreview(n);if(!s)return r.Z.json({success:!1,error:{type:"not_found",message:"Questionnaire not found"}},{status:404});return r.Z.json({success:!0,data:{questionnaire:{id:n,title:s.title,description:s.description,questionCount:s.questionCount,estimatedTime:s.estimatedTime,dimensions:s.dimensions},metadata:{createdAt:new Date().toISOString(),estimatedTime:s.estimatedTime,difficulty:"medium",tags:["octi-framework"]}}})}catch(e){return y.error("Failed to retrieve questionnaire",{error:e}),r.Z.json({success:!1,error:{type:"retrieval_error",message:e instanceof Error?e.message:"Failed to retrieve questionnaire"}},{status:500})}}async function w(e){try{let{questionDesignerAgent:e}=x(),t=e.getStats();return r.Z.json({success:!0,data:{cacheStats:t,supportedVersions:["standard","professional"],supportedQuestionTypes:["single_choice","multiple_choice","scale","text"],maxQuestionCount:100,minQuestionCount:10,supportedDimensions:["organizational_culture","leadership_effectiveness","team_dynamics","innovation_capability"]}})}catch(e){return y.error("Failed to get questionnaire options",{error:e}),r.Z.json({success:!1,error:{type:"options_error",message:"Failed to retrieve options"}},{status:500})}}let _=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/questionnaire/generate/route",pathname:"/api/questionnaire/generate",filename:"route",bundlePath:"app/api/questionnaire/generate/route"},resolvedPagePath:"/Users/<USER>/Documents/2.1 AI Journey/Cursor_projects/octi_test/src/app/api/questionnaire/generate/route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:D,staticGenerationAsyncStorage:T,serverHooks:Z,headerHooks:R,staticGenerationBailout:j}=_,C="/api/questionnaire/generate/route";function S(){return(0,o.patchFetch)({serverHooks:Z,staticGenerationAsyncStorage:T})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),i=t.X(0,[638,206,252,447],()=>n(3623));module.exports=i})();