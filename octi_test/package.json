{"name": "octi-assessment-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "14.0.4", "react": "^18", "react-dom": "^18", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5", "tailwindcss": "^3.3.0", "autoprefixer": "^10.0.1", "postcss": "^8", "eslint": "^8", "eslint-config-next": "14.0.4", "prisma": "^5.7.0", "@prisma/client": "^5.7.0", "redis": "^4.6.0", "zod": "^3.22.0", "axios": "^1.6.0", "zustand": "^4.4.0", "react-hook-form": "^7.48.0", "@hookform/resolvers": "^3.3.0", "winston": "^3.11.0"}, "devDependencies": {"@types/jest": "^29.5.0", "jest": "^29.5.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.0", "prettier": "^3.1.0"}}