# 重复文件清理检查清单

## 🎯 清理目标
统一使用 kebab-case 文件命名规范，删除 PascalCase 重复文件。

## ✅ 清理步骤

### 1. 文件重命名/删除
- [ ] 删除 `src/services/data/DataFusionEngine.ts`
- [ ] 保留 `src/services/data/data-fusion-engine.ts`
- [ ] 删除 `src/services/llm/LLMApiClient.ts`
- [ ] 保留 `src/services/llm/llm-api-client.ts`
- [ ] 删除 `src/services/llm/PromptBuilder.ts`
- [ ] 保留 `src/services/llm/prompt-builder.ts`

### 2. 导入引用更新
- [ ] 更新 `src/services/agents/QuestionDesignerAgent.ts`
- [ ] 更新 `src/services/agents/OrganizationTutorAgent.ts`
- [ ] 更新 `src/services/agents/AgentService.ts`
- [ ] 更新 `src/app/api/questionnaire/generate/route.ts`
- [ ] 更新 `src/app/api/assessment/evaluate/route.ts`
- [ ] 搜索并更新所有其他引用文件

### 3. 验证步骤
- [ ] 运行 `npm run type-check` 检查类型错误
- [ ] 运行 `npm run build` 检查构建错误
- [ ] 运行 `npm run test` 检查测试通过
- [ ] 检查 IDE 中是否还有红色错误提示

### 4. 清理命令
```bash
# 执行清理脚本
chmod +x scripts/cleanup-duplicate-files.sh
./scripts/cleanup-duplicate-files.sh

# 验证清理结果
find src -name "*DataFusionEngine*" -o -name "*LLMApiClient*" -o -name "*PromptBuilder*"

# 检查导入引用
grep -r "DataFusionEngine\|LLMApiClient\|PromptBuilder" src/ --include="*.ts"
```

## 🚨 注意事项
1. **备份重要文件** - 清理前确保代码已提交到Git
2. **内容对比** - 确认重复文件内容完全一致
3. **导入路径** - 更新所有相关的导入路径
4. **类型检查** - 清理后必须通过TypeScript类型检查

## 📊 清理效果
- ✅ 统一文件命名规范
- ✅ 减少代码重复
- ✅ 提高项目维护性
- ✅ 符合项目开发规范